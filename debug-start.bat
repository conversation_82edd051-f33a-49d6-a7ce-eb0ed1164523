@echo off
REM 🤖 BEGINNER-FRIENDLY DEBUG STARTUP SCRIPT (Windows)
REM This script starts debugging with minimal setup required

echo 🤖 BEGINNER-FRIENDLY DEBUG SETUP
echo ================================
echo.
echo Starting your dashboard with automatic issue detection...
echo.

REM Check if we're in the right directory
if not exist "package.json" (
    echo ❌ Error: Please run this script from your project root directory
    echo    ^(The folder that contains package.json^)
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing dependencies first...
    call npm install
)

echo 🔧 Adding Debug Panel to Dashboard...

REM Check if Dashboard file exists
if exist "src\pages\Dashboard.tsx" (
    findstr /C:"DebugPanel" "src\pages\Dashboard.tsx" >nul
    if errorlevel 1 (
        echo    Adding Debug Panel to Dashboard...
        echo    ^(This may require manual setup^)
        echo ⚠️  You may need to manually add the Debug Panel to your Dashboard
    ) else (
        echo ✅ Debug Panel already exists!
    )
) else (
    echo ⚠️  Could not find Dashboard.tsx - you may need to add the Debug Panel manually
)

echo.
echo 🚀 Starting development server with debugging enabled...
echo.
echo 👀 WHAT TO LOOK FOR:
echo - A 'Smart Debug Assistant' panel in the bottom-right corner
echo - Red alerts for critical issues
echo - Auto-fix buttons for common problems
echo.
echo 🔧 IF YOU SEE ISSUES:
echo 1. Read the description ^(explains what's wrong^)
echo 2. Click 'Auto-Fix This Issue' if available
echo 3. If that doesn't work, refresh the page ^(F5^)
echo.
echo 📖 For detailed help, see: BEGINNER_SETUP.md
echo.
echo Starting in 3 seconds...
timeout /t 3 /nobreak >nul

REM Start the development server with debug flags
set DEBUG=dashboard:*,supabase:*,musickit:*
start "Debug Server" cmd /c "npm run dev"

REM Wait a moment for the server to start
timeout /t 5 /nobreak >nul

REM Try to open the browser
set URL=http://localhost:3000/dashboard
echo.
echo 🌐 Opening browser to: %URL%
start "" "%URL%"

echo.
echo 🤖 The Smart Debug Assistant will guide you from here!
echo.
echo Press any key to close this window ^(the debug server will keep running^)
pause >nul
