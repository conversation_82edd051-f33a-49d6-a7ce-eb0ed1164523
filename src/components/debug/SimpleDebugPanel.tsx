import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/AuthContext';
import { useMusicStore } from '@/stores/musicStore';
import { useDashboardData } from '@/hooks/useDashboardData';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Bug, RefreshCw, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';

interface SimpleDebugPanelProps {
  className?: string;
}

// 🤖 SIMPLE DEBUG PANEL - No infinite loops, just basic status
export function SimpleDebugPanel({ className }: SimpleDebugPanelProps) {
  const [isOpen, setIsOpen] = useState(true);

  const { user, loading: authLoading } = useAuth();
  const { connectionState, isAuthorized, error: musicError } = useMusicStore();
  const { sessions, subscriptions, loading: dataLoading, error: dataError, fetchAllData } = useDashboardData(user?.id);

  // Simple status calculation - no complex logic
  const getSimpleStatus = () => {
    if (authLoading || dataLoading) {
      return { color: 'text-blue-400', icon: RefreshCw, message: 'Loading...', spinning: true };
    }
    
    if (dataError || musicError) {
      return { color: 'text-red-400', icon: XCircle, message: 'Issues detected', spinning: false };
    }
    
    if (!user) {
      return { color: 'text-yellow-400', icon: AlertTriangle, message: 'Not logged in', spinning: false };
    }
    
    return { color: 'text-green-400', icon: CheckCircle, message: 'All good!', spinning: false };
  };

  const status = getSimpleStatus();

  const handleRefresh = () => {
    console.log('🔧 Simple Debug: Refreshing data...');
    fetchAllData();
  };

  const handleShowInfo = () => {
    const info = {
      user: !!user,
      authLoading,
      dataLoading,
      sessionsCount: sessions?.length || 0,
      subscriptionsCount: subscriptions?.length || 0,
      musicState: connectionState,
      musicAuthorized: isAuthorized,
      errors: {
        dataError: dataError?.message || 'none',
        musicError: musicError?.message || 'none'
      }
    };
    
    console.log('🔧 Simple Debug Info:', info);
    alert('Debug info logged to console. Press F12 to see details.');
  };

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className={`fixed bottom-4 right-4 z-50 max-w-sm ${className}`}>
      <Card className="bg-gray-900/95 border-gray-600 text-white">
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-2 cursor-pointer hover:bg-gray-800/50">
              <CardTitle className="flex items-center gap-2 text-sm">
                <status.icon className={`w-4 h-4 ${status.color} ${status.spinning ? 'animate-spin' : ''}`} />
                🔧 Simple Debug
                {isOpen ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
              </CardTitle>
              <div className={`text-xs ${status.color} font-medium`}>
                {status.message}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          
          <CollapsibleContent>
            <CardContent className="space-y-3 text-xs">
              {/* Simple Status Grid */}
              <div className="grid grid-cols-2 gap-2">
                <div className="flex justify-between">
                  <span>Login:</span>
                  <Badge variant={user && !authLoading ? "default" : "destructive"}>
                    {user && !authLoading ? 'OK' : 'Issue'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Data:</span>
                  <Badge variant={!dataLoading && !dataError ? "default" : "destructive"}>
                    {!dataLoading && !dataError ? 'OK' : 'Issue'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Music:</span>
                  <Badge variant={connectionState === 'connected' ? "default" : "secondary"}>
                    {connectionState === 'connected' ? 'OK' : connectionState}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Sessions:</span>
                  <Badge variant="outline">{sessions?.length || 0}</Badge>
                </div>
              </div>

              {/* Error Messages */}
              {dataError && (
                <div className="bg-red-500/10 border border-red-500/30 rounded p-2">
                  <div className="text-red-400 text-xs font-medium">Data Error:</div>
                  <div className="text-red-300 text-xs">{dataError.message}</div>
                </div>
              )}

              {musicError && (
                <div className="bg-red-500/10 border border-red-500/30 rounded p-2">
                  <div className="text-red-400 text-xs font-medium">Music Error:</div>
                  <div className="text-red-300 text-xs">{musicError.message}</div>
                </div>
              )}

              {/* Simple Actions */}
              <div className="flex gap-2 pt-2 border-t border-gray-700">
                <Button size="sm" variant="outline" onClick={handleRefresh} className="flex-1">
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Refresh
                </Button>
                <Button size="sm" variant="outline" onClick={handleShowInfo} className="flex-1">
                  <Bug className="w-3 h-3 mr-1" />
                  Info
                </Button>
              </div>

              {/* Simple Tips */}
              <div className="bg-blue-500/10 border border-blue-500/30 rounded p-2">
                <div className="text-blue-400 text-xs font-medium mb-1">💡 Quick Tips:</div>
                <div className="text-blue-300 text-xs space-y-1">
                  <div>• Green = working, Red = problem</div>
                  <div>• Use "Refresh" if data won't load</div>
                  <div>• Use "Info" to see detailed status</div>
                  <div>• Press F12 to see console logs</div>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
}

export default SimpleDebugPanel;
