import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/AuthContext';
import { useMusicStore } from '@/stores/musicStore';
import { useDashboardData } from '@/hooks/useDashboardData';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Bug, RefreshCw } from 'lucide-react';

interface DebugPanelProps {
  className?: string;
}

export function DebugPanel({ className }: DebugPanelProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [renderCount, setRenderCount] = useState(0);
  const [lastUpdate, setLastUpdate] = useState(Date.now());

  const { user, loading: authLoading } = useAuth();
  const { connectionState, isAuthorized, error: musicError } = useMusicStore();
  const { sessions, subscriptions, loading: dataLoading, error: dataError, fetchAllData } = useDashboardData(user?.id);

  // Track renders
  useEffect(() => {
    setRenderCount(prev => prev + 1);
    setLastUpdate(Date.now());
  });

  const getStatusBadge = (condition: boolean, trueText: string, falseText: string) => (
    <Badge variant={condition ? "default" : "destructive"}>
      {condition ? trueText : falseText}
    </Badge>
  );

  const debugInfo = {
    auth: {
      hasUser: !!user,
      userId: user?.id || 'null',
      authLoading,
    },
    data: {
      dataLoading,
      sessionsCount: sessions?.length || 0,
      subscriptionsCount: subscriptions?.length || 0,
      hasDataError: !!dataError,
      dataErrorMessage: dataError?.message || 'none',
    },
    musicKit: {
      connectionState,
      isAuthorized,
      hasMusicError: !!musicError,
      musicErrorMessage: musicError?.message || 'none',
    },
    performance: {
      renderCount,
      lastUpdate: new Date(lastUpdate).toLocaleTimeString(),
    }
  };

  const handleForceRefresh = () => {
    console.log('🔧 DEBUG: Force refreshing dashboard data');
    fetchAllData();
  };

  const handleClearConsole = () => {
    console.clear();
    console.log('🔧 DEBUG: Console cleared at', new Date().toISOString());
  };

  const handleLogState = () => {
    console.group('🔧 DEBUG: Current State Dump');
    console.log('Auth State:', debugInfo.auth);
    console.log('Data State:', debugInfo.data);
    console.log('MusicKit State:', debugInfo.musicKit);
    console.log('Performance:', debugInfo.performance);
    console.log('Full Sessions:', sessions);
    console.log('Full Subscriptions:', subscriptions);
    console.groupEnd();
  };

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className={`fixed bottom-4 right-4 z-50 max-w-sm ${className}`}>
      <Card className="bg-gray-900/95 border-yellow-500/50 text-white">
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-2 cursor-pointer hover:bg-gray-800/50">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Bug className="w-4 h-4 text-yellow-500" />
                Debug Panel
                {isOpen ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          
          <CollapsibleContent>
            <CardContent className="space-y-3 text-xs">
              {/* Auth Status */}
              <div>
                <div className="font-medium text-yellow-400 mb-1">Authentication</div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>User:</span>
                    {getStatusBadge(debugInfo.auth.hasUser, 'Logged In', 'Not Logged In')}
                  </div>
                  <div className="flex justify-between">
                    <span>Loading:</span>
                    {getStatusBadge(!debugInfo.auth.authLoading, 'Ready', 'Loading')}
                  </div>
                  {debugInfo.auth.userId !== 'null' && (
                    <div className="text-gray-400 text-xs">ID: {debugInfo.auth.userId.slice(0, 8)}...</div>
                  )}
                </div>
              </div>

              {/* Data Status */}
              <div>
                <div className="font-medium text-blue-400 mb-1">Data</div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Loading:</span>
                    {getStatusBadge(!debugInfo.data.dataLoading, 'Ready', 'Loading')}
                  </div>
                  <div className="flex justify-between">
                    <span>Sessions:</span>
                    <Badge variant="outline">{debugInfo.data.sessionsCount}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Subscriptions:</span>
                    <Badge variant="outline">{debugInfo.data.subscriptionsCount}</Badge>
                  </div>
                  {debugInfo.data.hasDataError && (
                    <div className="text-red-400 text-xs">Error: {debugInfo.data.dataErrorMessage}</div>
                  )}
                </div>
              </div>

              {/* MusicKit Status */}
              <div>
                <div className="font-medium text-purple-400 mb-1">MusicKit</div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>State:</span>
                    <Badge variant={debugInfo.musicKit.connectionState === 'connected' ? 'default' : 'secondary'}>
                      {debugInfo.musicKit.connectionState}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Authorized:</span>
                    {getStatusBadge(debugInfo.musicKit.isAuthorized, 'Yes', 'No')}
                  </div>
                  {debugInfo.musicKit.hasMusicError && (
                    <div className="text-red-400 text-xs">Error: {debugInfo.musicKit.musicErrorMessage}</div>
                  )}
                </div>
              </div>

              {/* Performance */}
              <div>
                <div className="font-medium text-green-400 mb-1">Performance</div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Renders:</span>
                    <Badge variant={debugInfo.performance.renderCount > 10 ? 'destructive' : 'outline'}>
                      {debugInfo.performance.renderCount}
                    </Badge>
                  </div>
                  <div className="text-gray-400 text-xs">
                    Last: {debugInfo.performance.lastUpdate}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-2 border-t border-gray-700">
                <Button size="sm" variant="outline" onClick={handleForceRefresh} className="flex-1">
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Refresh
                </Button>
                <Button size="sm" variant="outline" onClick={handleLogState} className="flex-1">
                  Log State
                </Button>
                <Button size="sm" variant="outline" onClick={handleClearConsole} className="flex-1">
                  Clear
                </Button>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
}

export default DebugPanel;
