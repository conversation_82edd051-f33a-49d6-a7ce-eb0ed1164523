import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/AuthContext';
import { useMusicStore } from '@/stores/musicStore';
import { useDashboardData } from '@/hooks/useDashboardData';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Bug, RefreshCw, AlertTriangle, CheckCircle, XCircle, Zap } from 'lucide-react';
import { subscribeToIssues, clearIssues } from '@/utils/debug';

// 🤖 BEGINNER-FRIENDLY AUTOMATED DEBUG PANEL
// This panel automatically detects problems and tells you how to fix them
// No technical knowledge required!

interface DebugPanelProps {
  className?: string;
}

export function DebugPanel({ className }: DebugPanelProps) {
  const [isOpen, setIsOpen] = useState(true); // Start open for beginners
  const [renderCount, setRenderCount] = useState(0);
  const [lastUpdate, setLastUpdate] = useState(Date.now());
  const [detectedIssues, setDetectedIssues] = useState<any[]>([]);
  const [isFixing, setIsFixing] = useState(false);

  const { user, loading: authLoading } = useAuth();
  const { connectionState, isAuthorized, error: musicError } = useMusicStore();
  const { sessions, subscriptions, loading: dataLoading, error: dataError, fetchAllData } = useDashboardData(user?.id);

  // 🤖 AUTOMATIC ISSUE DETECTION
  useEffect(() => {
    const unsubscribe = subscribeToIssues((issues) => {
      setDetectedIssues(issues);
    });

    return unsubscribe;
  }, []);

  // Track renders and detect excessive re-rendering
  useEffect(() => {
    setRenderCount(prev => prev + 1);
    setLastUpdate(Date.now());
  });

  // Separate effect for excessive render detection to prevent loops
  useEffect(() => {
    if (renderCount > 15) {
      // This will be caught by the debug system
      console.warn('Excessive renders detected in DebugPanel', { renderCount });
    }
  }, [renderCount]);

  // 🌐 EXPOSE GLOBAL FUNCTIONS FOR AUTO-FIXES
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).__forceDashboardRefresh = fetchAllData;
      (window as any).__reconnectMusicKit = async () => {
        // This would trigger MusicKit reconnection
        console.log('🔧 Auto-fix: Reconnecting MusicKit...');
      };
    }
  }, [fetchAllData]);

  const getStatusBadge = (condition: boolean, trueText: string, falseText: string) => (
    <Badge variant={condition ? "default" : "destructive"}>
      {condition ? trueText : falseText}
    </Badge>
  );

  // 🎯 SMART STATUS ANALYSIS - Automatically determines what's wrong
  const getOverallStatus = () => {
    const criticalIssues = detectedIssues.filter(issue => issue.severity === 'critical');
    const warningIssues = detectedIssues.filter(issue => issue.severity === 'warning');

    if (criticalIssues.length > 0) {
      return {
        status: 'critical',
        message: `${criticalIssues.length} critical issue${criticalIssues.length > 1 ? 's' : ''} found`,
        color: 'text-red-400',
        icon: XCircle
      };
    }

    if (warningIssues.length > 0) {
      return {
        status: 'warning',
        message: `${warningIssues.length} warning${warningIssues.length > 1 ? 's' : ''}`,
        color: 'text-yellow-400',
        icon: AlertTriangle
      };
    }

    if (authLoading || dataLoading) {
      return {
        status: 'loading',
        message: 'Loading dashboard...',
        color: 'text-blue-400',
        icon: RefreshCw
      };
    }

    return {
      status: 'good',
      message: 'Everything looks good!',
      color: 'text-green-400',
      icon: CheckCircle
    };
  };

  const overallStatus = getOverallStatus();

  const debugInfo = {
    auth: {
      hasUser: !!user,
      userId: user?.id || 'null',
      authLoading,
    },
    data: {
      dataLoading,
      sessionsCount: sessions?.length || 0,
      subscriptionsCount: subscriptions?.length || 0,
      hasDataError: !!dataError,
      dataErrorMessage: dataError?.message || 'none',
    },
    musicKit: {
      connectionState,
      isAuthorized,
      hasMusicError: !!musicError,
      musicErrorMessage: musicError?.message || 'none',
    },
    performance: {
      renderCount,
      lastUpdate: new Date(lastUpdate).toLocaleTimeString(),
    }
  };

  // 🔧 AUTOMATED FIX FUNCTIONS - One-click solutions for common problems
  const handleAutoFix = async (issue: any) => {
    if (!issue.autoFix) return;

    setIsFixing(true);
    try {
      await issue.autoFix();
      // Remove the fixed issue
      setDetectedIssues(prev => prev.filter(i => i.id !== issue.id));
    } catch (error) {
      console.error('Auto-fix failed:', error);
    } finally {
      setIsFixing(false);
    }
  };

  const handleForceRefresh = () => {
    console.log('🔧 BEGINNER DEBUG: Refreshing dashboard data...');
    fetchAllData();
  };

  const handleClearIssues = () => {
    clearIssues();
    console.log('🔧 BEGINNER DEBUG: Cleared all detected issues');
  };

  const handleGetHelp = () => {
    const helpText = `
🆘 DASHBOARD DEBUG HELP

Current Status: ${overallStatus.message}

Issues Found: ${detectedIssues.length}
${detectedIssues.map(issue => `- ${issue.title}: ${issue.description}`).join('\n')}

What to do next:
1. Try clicking the "Auto-Fix" buttons for any critical issues
2. If that doesn't work, try refreshing the page
3. If problems persist, check your internet connection

Technical Details (for developers):
- Auth Loading: ${authLoading}
- Data Loading: ${dataLoading}
- Sessions Count: ${sessions?.length || 0}
- MusicKit State: ${connectionState}
- Render Count: ${renderCount}
    `;

    console.log(helpText);
    alert('Help information has been logged to the console. Press F12 to see it.');
  };

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className={`fixed bottom-4 right-4 z-50 max-w-md ${className}`}>
      <Card className={`bg-gray-900/95 text-white border-2 ${
        overallStatus.status === 'critical' ? 'border-red-500' :
        overallStatus.status === 'warning' ? 'border-yellow-500' :
        overallStatus.status === 'loading' ? 'border-blue-500' :
        'border-green-500'
      }`}>
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-2 cursor-pointer hover:bg-gray-800/50">
              <CardTitle className="flex items-center gap-2 text-sm">
                <overallStatus.icon className={`w-4 h-4 ${overallStatus.color} ${overallStatus.status === 'loading' ? 'animate-spin' : ''}`} />
                🤖 Smart Debug Assistant
                {detectedIssues.length > 0 && (
                  <Badge variant="destructive" className="ml-2">
                    {detectedIssues.length}
                  </Badge>
                )}
                {isOpen ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
              </CardTitle>
              <div className={`text-xs ${overallStatus.color} font-medium`}>
                {overallStatus.message}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          
          <CollapsibleContent>
            <CardContent className="space-y-4 text-xs">
              {/* 🚨 DETECTED ISSUES SECTION - Most Important */}
              {detectedIssues.length > 0 && (
                <div className="border border-red-500/30 rounded-lg p-3 bg-red-500/10">
                  <div className="font-medium text-red-400 mb-2 flex items-center gap-2">
                    <AlertTriangle className="w-4 h-4" />
                    Issues Found ({detectedIssues.length})
                  </div>
                  <div className="space-y-3">
                    {detectedIssues.slice(0, 3).map((issue, index) => (
                      <div key={issue.id} className="border border-gray-600 rounded p-2 bg-gray-800/50">
                        <div className="flex items-start gap-2 mb-2">
                          {issue.severity === 'critical' ? (
                            <XCircle className="w-4 h-4 text-red-400 flex-shrink-0 mt-0.5" />
                          ) : issue.severity === 'warning' ? (
                            <AlertTriangle className="w-4 h-4 text-yellow-400 flex-shrink-0 mt-0.5" />
                          ) : (
                            <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0 mt-0.5" />
                          )}
                          <div className="flex-1">
                            <div className="font-medium text-white">{issue.title}</div>
                            <div className="text-gray-300 text-xs mt-1">{issue.description}</div>
                            <div className="text-blue-300 text-xs mt-1">💡 {issue.solution}</div>
                            {issue.fileLocation && (
                              <div className="text-gray-400 text-xs mt-1">📁 {issue.fileLocation}</div>
                            )}
                          </div>
                        </div>
                        {issue.autoFix && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleAutoFix(issue)}
                            disabled={isFixing}
                            className="w-full mt-2"
                          >
                            {isFixing ? (
                              <>
                                <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                                Fixing...
                              </>
                            ) : (
                              <>
                                <Zap className="w-3 h-3 mr-1" />
                                Auto-Fix This Issue
                              </>
                            )}
                          </Button>
                        )}
                      </div>
                    ))}
                    {detectedIssues.length > 3 && (
                      <div className="text-gray-400 text-center">
                        ... and {detectedIssues.length - 3} more issues
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Quick Status Overview */}
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex justify-between">
                  <span>Login:</span>
                  {getStatusBadge(!!user && !authLoading, 'OK', 'Issue')}
                </div>
                <div className="flex justify-between">
                  <span>Data:</span>
                  {getStatusBadge(!dataLoading && !dataError, 'OK', 'Issue')}
                </div>
                <div className="flex justify-between">
                  <span>Music:</span>
                  {getStatusBadge(connectionState === 'connected', 'OK', 'Issue')}
                </div>
                <div className="flex justify-between">
                  <span>Performance:</span>
                  {getStatusBadge(renderCount < 15, 'OK', 'Slow')}
                </div>
              </div>

              {/* Data Status */}
              <div>
                <div className="font-medium text-blue-400 mb-1">Data</div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Loading:</span>
                    {getStatusBadge(!debugInfo.data.dataLoading, 'Ready', 'Loading')}
                  </div>
                  <div className="flex justify-between">
                    <span>Sessions:</span>
                    <Badge variant="outline">{debugInfo.data.sessionsCount}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Subscriptions:</span>
                    <Badge variant="outline">{debugInfo.data.subscriptionsCount}</Badge>
                  </div>
                  {debugInfo.data.hasDataError && (
                    <div className="text-red-400 text-xs">Error: {debugInfo.data.dataErrorMessage}</div>
                  )}
                </div>
              </div>

              {/* MusicKit Status */}
              <div>
                <div className="font-medium text-purple-400 mb-1">MusicKit</div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>State:</span>
                    <Badge variant={debugInfo.musicKit.connectionState === 'connected' ? 'default' : 'secondary'}>
                      {debugInfo.musicKit.connectionState}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Authorized:</span>
                    {getStatusBadge(debugInfo.musicKit.isAuthorized, 'Yes', 'No')}
                  </div>
                  {debugInfo.musicKit.hasMusicError && (
                    <div className="text-red-400 text-xs">Error: {debugInfo.musicKit.musicErrorMessage}</div>
                  )}
                </div>
              </div>

              {/* Performance */}
              <div>
                <div className="font-medium text-green-400 mb-1">Performance</div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Renders:</span>
                    <Badge variant={debugInfo.performance.renderCount > 10 ? 'destructive' : 'outline'}>
                      {debugInfo.performance.renderCount}
                    </Badge>
                  </div>
                  <div className="text-gray-400 text-xs">
                    Last: {debugInfo.performance.lastUpdate}
                  </div>
                </div>
              </div>

              {/* 🔧 BEGINNER-FRIENDLY ACTIONS */}
              <div className="space-y-2 pt-2 border-t border-gray-700">
                <div className="text-gray-400 text-xs font-medium">Quick Actions:</div>
                <div className="grid grid-cols-2 gap-2">
                  <Button size="sm" variant="outline" onClick={handleForceRefresh}>
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Refresh Data
                  </Button>
                  <Button size="sm" variant="outline" onClick={handleClearIssues}>
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Clear Issues
                  </Button>
                </div>
                <Button size="sm" variant="outline" onClick={handleGetHelp} className="w-full">
                  <Bug className="w-3 h-3 mr-1" />
                  Get Help & Copy Debug Info
                </Button>

                {/* Beginner Tips */}
                <div className="bg-blue-500/10 border border-blue-500/30 rounded p-2 mt-3">
                  <div className="text-blue-400 text-xs font-medium mb-1">💡 Beginner Tips:</div>
                  <div className="text-blue-300 text-xs space-y-1">
                    <div>• Red issues need immediate attention</div>
                    <div>• Yellow warnings can usually be ignored</div>
                    <div>• Try "Auto-Fix" buttons first</div>
                    <div>• If stuck, refresh the page (F5)</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
}

export default DebugPanel;
