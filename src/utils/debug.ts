// Debug utility for dashboard debugging
// Usage: import { createDebugger } from '@/utils/debug';
// const debug = createDebugger('dashboard:data');

interface DebugConfig {
  enabled: boolean;
  namespace: string;
  colors: Record<string, string>;
  logLevel: 'error' | 'warn' | 'info' | 'debug' | 'trace';
}

const config: DebugConfig = {
  enabled: process.env.NODE_ENV === 'development',
  namespace: '',
  colors: {
    'dashboard:data': '#00ff00',
    'dashboard:realtime': '#ff6600', 
    'dashboard:musickit': '#ff0066',
    'dashboard:auth': '#0066ff',
    'supabase:query': '#ffff00',
    'supabase:realtime': '#ff00ff',
    'musickit:lifecycle': '#00ffff',
    'musickit:auth': '#ff3300'
  },
  logLevel: 'debug'
};

export function createDebugger(namespace: string) {
  const color = config.colors[namespace] || '#ffffff';
  
  const log = (level: string, message: string, ...args: any[]) => {
    if (!config.enabled) return;
    
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const prefix = `%c[${timestamp}] ${namespace}`;
    const style = `color: ${color}; font-weight: bold;`;
    
    console.groupCollapsed(prefix, style, message);
    if (args.length > 0) {
      args.forEach((arg, index) => {
        if (typeof arg === 'object') {
          console.log(`Arg ${index + 1}:`, JSON.stringify(arg, null, 2));
        } else {
          console.log(`Arg ${index + 1}:`, arg);
        }
      });
    }
    console.trace('Stack trace');
    console.groupEnd();
  };

  return {
    error: (message: string, ...args: any[]) => log('ERROR', message, ...args),
    warn: (message: string, ...args: any[]) => log('WARN', message, ...args),
    info: (message: string, ...args: any[]) => log('INFO', message, ...args),
    debug: (message: string, ...args: any[]) => log('DEBUG', message, ...args),
    trace: (message: string, ...args: any[]) => log('TRACE', message, ...args),
    
    // Special methods for common debugging scenarios
    apiCall: (method: string, url: string, data?: any) => {
      log('API', `${method} ${url}`, data);
    },
    
    stateChange: (component: string, oldState: any, newState: any) => {
      log('STATE', `${component} state change`, { from: oldState, to: newState });
    },
    
    lifecycle: (component: string, event: string, data?: any) => {
      log('LIFECYCLE', `${component}: ${event}`, data);
    },
    
    performance: (operation: string, startTime: number) => {
      const duration = Date.now() - startTime;
      log('PERF', `${operation} took ${duration}ms`);
    },
    
    // Method to create a timer
    timer: (label: string) => {
      const start = Date.now();
      return {
        end: () => {
          const duration = Date.now() - start;
          log('TIMER', `${label}: ${duration}ms`);
          return duration;
        }
      };
    }
  };
}

// Pre-configured debuggers for common namespaces
export const dashboardDataDebug = createDebugger('dashboard:data');
export const dashboardRealtimeDebug = createDebugger('dashboard:realtime');
export const dashboardMusicKitDebug = createDebugger('dashboard:musickit');
export const dashboardAuthDebug = createDebugger('dashboard:auth');
export const supabaseQueryDebug = createDebugger('supabase:query');
export const supabaseRealtimeDebug = createDebugger('supabase:realtime');
export const musicKitLifecycleDebug = createDebugger('musickit:lifecycle');
export const musicKitAuthDebug = createDebugger('musickit:auth');

// Global error handler for uncaught exceptions
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    createDebugger('global:error').error('Uncaught error', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error
    });
  });

  window.addEventListener('unhandledrejection', (event) => {
    createDebugger('global:promise').error('Unhandled promise rejection', {
      reason: event.reason,
      promise: event.promise
    });
  });
}
