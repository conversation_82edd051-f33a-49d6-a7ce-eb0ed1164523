// 🔧 BEGINNER-FRIENDLY DEBUG SYSTEM
// This file automatically detects and explains problems in plain English
// No technical knowledge required - just follow the guidance it provides

interface DebugConfig {
  enabled: boolean;
  namespace: string;
  colors: Record<string, string>;
  logLevel: 'error' | 'warn' | 'info' | 'debug' | 'trace';
}

// 🚨 AUTOMATIC ISSUE DETECTION SYSTEM
interface DetectedIssue {
  id: string;
  severity: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  solution: string;
  fileLocation?: string;
  autoFix?: () => Promise<void>;
  timestamp: number;
}

// Global issue tracker for the automated system
let detectedIssues: DetectedIssue[] = [];
let issueCallbacks: ((issues: DetectedIssue[]) => void)[] = [];

const config: DebugConfig = {
  enabled: false, // Temporarily disable all debug logging to test infinite loop
  namespace: '',
  colors: {
    'dashboard:data': '#00ff00',
    'dashboard:realtime': '#ff6600',
    'dashboard:musickit': '#ff0066',
    'dashboard:auth': '#0066ff',
    'supabase:query': '#ffff00',
    'supabase:realtime': '#ff00ff',
    'musickit:lifecycle': '#00ffff',
    'musickit:auth': '#ff3300'
  },
  logLevel: 'debug'
};

// 🤖 AUTOMATIC ISSUE DETECTION FUNCTIONS
export function addIssue(issue: Omit<DetectedIssue, 'timestamp'>) {
  const newIssue: DetectedIssue = {
    ...issue,
    timestamp: Date.now()
  };

  detectedIssues.push(newIssue);

  // Notify all listeners (like the Debug Panel)
  issueCallbacks.forEach(callback => callback([...detectedIssues]));

  // Show browser notification for critical issues
  if (issue.severity === 'critical' && typeof window !== 'undefined') {
    showBrowserNotification(issue.title, issue.description);
  }
}

export function clearIssues() {
  detectedIssues = [];
  issueCallbacks.forEach(callback => callback([]));
}

export function subscribeToIssues(callback: (issues: DetectedIssue[]) => void) {
  issueCallbacks.push(callback);
  // Immediately call with current issues
  callback([...detectedIssues]);

  // Return unsubscribe function
  return () => {
    const index = issueCallbacks.indexOf(callback);
    if (index > -1) {
      issueCallbacks.splice(index, 1);
    }
  };
}

function showBrowserNotification(title: string, description: string) {
  // Create a visual notification in the browser
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #dc2626;
    color: white;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 10000;
    max-width: 400px;
    font-family: system-ui, -apple-system, sans-serif;
  `;

  notification.innerHTML = `
    <div style="font-weight: bold; margin-bottom: 8px;">🚨 ${title}</div>
    <div style="font-size: 14px;">${description}</div>
    <button onclick="this.parentElement.remove()" style="
      position: absolute;
      top: 8px;
      right: 8px;
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 18px;
    ">×</button>
  `;

  document.body.appendChild(notification);

  // Auto-remove after 10 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.remove();
    }
  }, 10000);
}

export function createDebugger(namespace: string) {
  const color = config.colors[namespace] || '#ffffff';

  const log = (level: string, message: string, ...args: any[]) => {
    if (!config.enabled) return;

    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const prefix = `%c[${timestamp}] ${namespace}`;
    const style = `color: ${color}; font-weight: bold;`;

    console.groupCollapsed(prefix, style, message);
    if (args.length > 0) {
      args.forEach((arg, index) => {
        if (typeof arg === 'object') {
          console.log(`Arg ${index + 1}:`, JSON.stringify(arg, null, 2));
        } else {
          console.log(`Arg ${index + 1}:`, arg);
        }
      });
    }
    console.trace('Stack trace');
    console.groupEnd();

    // 🤖 AUTOMATIC ISSUE DETECTION
    detectIssuesFromLog(level, namespace, message, args);
  };

  // 🔍 SMART ISSUE DETECTION - Automatically finds problems and explains them
  function detectIssuesFromLog(level: string, namespace: string, message: string, args: any[]) {
    const messageText = message.toLowerCase();
    const argsText = JSON.stringify(args).toLowerCase();

    // 🚨 CRITICAL: Loading stuck forever
    if (namespace === 'dashboard:data' && messageText.includes('starting fetchalldata') && level === 'info') {
      // Set a timer to check if this completes
      setTimeout(() => {
        const hasCompletionLog = detectedIssues.some(issue =>
          issue.id.includes('fetchalldata-completion')
        );
        if (!hasCompletionLog) {
          addIssue({
            id: 'loading-stuck-' + Date.now(),
            severity: 'critical',
            title: '🔄 Dashboard Stuck Loading',
            description: 'Your dashboard started loading data but never finished. This means something went wrong while getting your sessions and subscription information.',
            solution: 'Click the "Fix Loading Issue" button below to restart the data loading process.',
            fileLocation: 'src/hooks/useDashboardData.ts around line 110',
            autoFix: async () => {
              // This will be connected to the actual fix function
              if (typeof window !== 'undefined' && (window as any).__forceDashboardRefresh) {
                (window as any).__forceDashboardRefresh();
              }
            }
          });
        }
      }, 5000); // Wait 5 seconds for completion
    }

    // ✅ Mark completion to prevent false positives
    if (namespace === 'dashboard:data' && messageText.includes('parallel query results')) {
      addIssue({
        id: 'fetchalldata-completion-' + Date.now(),
        severity: 'info',
        title: '✅ Data Loading Completed',
        description: 'Dashboard data loaded successfully!',
        solution: 'No action needed - everything is working correctly.'
      });
    }

    // 🚨 CRITICAL: Authentication problems
    if (level === 'error' && (messageText.includes('auth') || messageText.includes('401') || messageText.includes('unauthorized'))) {
      addIssue({
        id: 'auth-error-' + Date.now(),
        severity: 'critical',
        title: '🔐 Login Problem Detected',
        description: 'There\'s an issue with your login session. This usually means you need to log in again.',
        solution: 'Try refreshing the page. If that doesn\'t work, log out and log back in.',
        fileLocation: 'src/context/AuthContext.tsx',
        autoFix: async () => {
          if (typeof window !== 'undefined') {
            window.location.reload();
          }
        }
      });
    }

    // 🚨 CRITICAL: MusicKit initialization failed
    if (namespace.includes('musickit') && level === 'error' && messageText.includes('failed')) {
      addIssue({
        id: 'musickit-error-' + Date.now(),
        severity: 'critical',
        title: '🎵 Apple Music Connection Failed',
        description: 'The Apple Music player couldn\'t start properly. This might be due to network issues or Apple Music being unavailable.',
        solution: 'Try the "Reconnect Apple Music" button below, or check your internet connection.',
        fileLocation: 'src/hooks/useAppleMusicAuth.ts',
        autoFix: async () => {
          // This will trigger a MusicKit reconnection
          if (typeof window !== 'undefined' && (window as any).__reconnectMusicKit) {
            (window as any).__reconnectMusicKit();
          }
        }
      });
    }

    // ⚠️ WARNING: Too many re-renders
    if (messageText.includes('excessive renders') || messageText.includes('render count')) {
      const renderCount = args.find(arg => typeof arg === 'object' && arg.renderCount);
      if (renderCount && renderCount.renderCount > 10) {
        addIssue({
          id: 'excessive-renders-' + Date.now(),
          severity: 'warning',
          title: '⚡ Performance Issue Detected',
          description: `Your dashboard is re-rendering too many times (${renderCount.renderCount} times). This can make the app slow and drain battery.`,
          solution: 'This is usually caused by a programming issue. The app will still work, but it might be slower than normal.',
          fileLocation: 'Check the component that\'s re-rendering frequently'
        });
      }
    }
  }

  return {
    error: (message: string, ...args: any[]) => log('ERROR', message, ...args),
    warn: (message: string, ...args: any[]) => log('WARN', message, ...args),
    info: (message: string, ...args: any[]) => log('INFO', message, ...args),
    debug: (message: string, ...args: any[]) => log('DEBUG', message, ...args),
    trace: (message: string, ...args: any[]) => log('TRACE', message, ...args),
    
    // Special methods for common debugging scenarios
    apiCall: (method: string, url: string, data?: any) => {
      log('API', `${method} ${url}`, data);
    },
    
    stateChange: (component: string, oldState: any, newState: any) => {
      log('STATE', `${component} state change`, { from: oldState, to: newState });
    },
    
    lifecycle: (component: string, event: string, data?: any) => {
      log('LIFECYCLE', `${component}: ${event}`, data);
    },
    
    performance: (operation: string, startTime: number) => {
      const duration = Date.now() - startTime;
      log('PERF', `${operation} took ${duration}ms`);
    },
    
    // Method to create a timer
    timer: (label: string) => {
      const start = Date.now();
      return {
        end: () => {
          const duration = Date.now() - start;
          log('TIMER', `${label}: ${duration}ms`);
          return duration;
        }
      };
    }
  };
}

// Pre-configured debuggers for common namespaces
export const dashboardDataDebug = createDebugger('dashboard:data');
export const dashboardRealtimeDebug = createDebugger('dashboard:realtime');
export const dashboardMusicKitDebug = createDebugger('dashboard:musickit');
export const dashboardAuthDebug = createDebugger('dashboard:auth');
export const supabaseQueryDebug = createDebugger('supabase:query');
export const supabaseRealtimeDebug = createDebugger('supabase:realtime');
export const musicKitLifecycleDebug = createDebugger('musickit:lifecycle');
export const musicKitAuthDebug = createDebugger('musickit:auth');

// Global error handler for uncaught exceptions
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    createDebugger('global:error').error('Uncaught error', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error
    });
  });

  window.addEventListener('unhandledrejection', (event) => {
    createDebugger('global:promise').error('Unhandled promise rejection', {
      reason: event.reason,
      promise: event.promise
    });
  });
}
