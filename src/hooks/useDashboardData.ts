import { useState, useCallback, useRef, useEffect } from "react";
import { supabase } from '@/integrations/supabase/client';
import { SessionWithSettings } from '@/types/session';
import { dashboardDataDebug, dashboardRealtimeDebug, supabaseQueryDebug } from '@/utils/debug';

interface Subscription {
  id: string;
  dj_id: string;
  plan_id: string | null;
  status: string;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  current_period_start: string | null;
  current_period_end: string | null;
  cancel_at_period_end: boolean | null;
  created_at: string;
  updated_at: string;
}

export function useDashboardData(userId?: string) {
  const [sessions, setSessions] = useState<SessionWithSettings[]>([]);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const abortControllerRef = useRef<AbortController>();
  const subscriptionsRef = useRef<any[]>([]);

  // Debug tracking
  const hookInstanceId = useRef(Math.random().toString(36).slice(2, 11));
  const renderCountRef = useRef(0);
  const hasLoggedRef = useRef<string | undefined>();

  renderCountRef.current++;

  // Only log once when userId changes
  if (hasLoggedRef.current !== userId) {
    dashboardDataDebug.lifecycle('useDashboardData', 'Hook initialized', {
      userId,
      instanceId: hookInstanceId.current,
      renderCount: renderCountRef.current
    });
    hasLoggedRef.current = userId;
  }

  // Debug excessive renders
  if (renderCountRef.current > 5) {
    dashboardDataDebug.warn('Excessive renders detected', {
      renderCount: renderCountRef.current,
      userId,
      instanceId: hookInstanceId.current
    });
  }

  const fetchSessionsOnly = useCallback(async () => {
    if (!userId) {
      dashboardDataDebug.warn('fetchSessionsOnly called without userId');
      return;
    }

    const timer = dashboardDataDebug.timer('fetchSessionsOnly');
    dashboardDataDebug.info('Starting fetchSessionsOnly', { userId, instanceId: hookInstanceId.current });

    // Cancel any in-progress fetches
    if (abortControllerRef.current) {
      dashboardDataDebug.debug('Aborting previous fetch');
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this fetch
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);
    dashboardDataDebug.stateChange('useDashboardData', { loading: false }, { loading: true });

    try {
      supabaseQueryDebug.apiCall('GET', 'sessions', { dj_id: userId });

      const sessionsResult = await supabase
        .from('sessions')
        .select('*')
        .eq('dj_id', userId)
        .order('created_at', { ascending: false });

      dashboardDataDebug.debug('Sessions query result', {
        error: sessionsResult.error,
        dataLength: sessionsResult.data?.length,
        status: sessionsResult.status,
        statusText: sessionsResult.statusText
      });

      // Handle any errors
      if (sessionsResult.error) throw sessionsResult.error;

      // Update sessions state only
      setSessions(sessionsResult.data || []);
      dashboardDataDebug.stateChange('sessions', sessions, sessionsResult.data || []);

    } catch (err) {
      dashboardDataDebug.error('Failed to fetch sessions', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch sessions'));
    } finally {
      setLoading(false);
      abortControllerRef.current = undefined;
      dashboardDataDebug.stateChange('useDashboardData', { loading: true }, { loading: false });
      timer.end();
    }
  }, [userId]);

  const fetchAllData = useCallback(async () => {
    if (!userId) {
      dashboardDataDebug.warn('fetchAllData called without userId');
      return;
    }

    const timer = dashboardDataDebug.timer('fetchAllData');
    dashboardDataDebug.info('Starting fetchAllData', { userId, instanceId: hookInstanceId.current });

    // Cancel any in-progress fetches
    if (abortControllerRef.current) {
      dashboardDataDebug.debug('Aborting previous fetch');
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this fetch
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);
    dashboardDataDebug.stateChange('useDashboardData', { loading: false }, { loading: true });

    try {
      supabaseQueryDebug.apiCall('GET', 'parallel queries', {
        sessions: { dj_id: userId },
        subscriptions: { dj_id: userId }
      });

      // Fetch sessions and subscriptions in parallel
      const [sessionsResult, subscriptionsResult] = await Promise.all([
        supabase
          .from('sessions')
          .select('*')
          .eq('dj_id', userId)
          .order('created_at', { ascending: false }),
        supabase
          .from('dj_subscriptions')
          .select('*')
          .eq('dj_id', userId)
      ]);

      dashboardDataDebug.debug('Parallel query results', {
        sessions: {
          error: sessionsResult.error,
          dataLength: sessionsResult.data?.length,
          status: sessionsResult.status
        },
        subscriptions: {
          error: subscriptionsResult.error,
          dataLength: subscriptionsResult.data?.length,
          status: subscriptionsResult.status
        }
      });

      // Handle any errors
      if (sessionsResult.error) throw sessionsResult.error;
      if (subscriptionsResult.error) throw subscriptionsResult.error;

      // Update state with new data
      const newSessions = sessionsResult.data || [];
      const newSubscriptions = subscriptionsResult.data || [];

      setSessions(newSessions);
      setSubscriptions(newSubscriptions);

      dashboardDataDebug.stateChange('sessions', sessions, newSessions);
      dashboardDataDebug.stateChange('subscriptions', subscriptions, newSubscriptions);

    } catch (err) {
      dashboardDataDebug.error('Failed to fetch dashboard data', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch data'));
    } finally {
      setLoading(false);
      abortControllerRef.current = undefined;
      dashboardDataDebug.stateChange('useDashboardData', { loading: true }, { loading: false });
      timer.end();
    }
  }, [userId]);

  // Fetch data and set up subscriptions when userId changes
  useEffect(() => {
    if (!userId) {
      dashboardDataDebug.warn('useEffect called without userId');
      return;
    }

    dashboardDataDebug.lifecycle('useDashboardData', 'Setting up subscriptions', {
      userId,
      instanceId: hookInstanceId.current
    });

    // Fetch initial data
    fetchAllData();

    // Set up realtime subscriptions
    const sessionChangesChannel = supabase
      .channel(`dashboard-session-changes-${userId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sessions',
        filter: `dj_id=eq.${userId}`
      }, (payload: any) => {
        dashboardRealtimeDebug.info('Session change detected', {
          event: payload.eventType,
          table: payload.table,
          new: payload.new,
          old: payload.old,
          userId
        });
        fetchAllData();
      })
      .subscribe();

    const subscriptionChangesChannel = supabase
      .channel(`dashboard-subscription-changes-${userId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'dj_subscriptions',
        filter: `dj_id=eq.${userId}`
      }, (payload: any) => {
        dashboardRealtimeDebug.info('Subscription change detected', {
          event: payload.eventType,
          table: payload.table,
          new: payload.new,
          old: payload.old,
          userId
        });
        fetchAllData();
      })
      .subscribe();

    // Store subscriptions in ref for cleanup
    subscriptionsRef.current = [sessionChangesChannel, subscriptionChangesChannel];

    dashboardRealtimeDebug.debug('Realtime subscriptions created', {
      sessionChannelId: sessionChangesChannel.topic,
      subscriptionChannelId: subscriptionChangesChannel.topic,
      userId
    });

    return () => {
      dashboardDataDebug.lifecycle('useDashboardData', 'Cleaning up subscriptions', {
        userId,
        instanceId: hookInstanceId.current
      });
      subscriptionsRef.current.forEach((channel: any) => {
        dashboardRealtimeDebug.debug('Removing channel', { topic: channel.topic });
        supabase.removeChannel(channel);
      });
      subscriptionsRef.current = [];
    };
  }, [userId]); // Remove fetchAllData to prevent infinite loops

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    sessions,
    subscriptions,
    loading,
    error,
    fetchAllData,
    fetchSessionsOnly,
  };
}
