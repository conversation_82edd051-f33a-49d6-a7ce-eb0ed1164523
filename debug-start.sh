#!/bin/bash

# 🤖 BEGINNER-FRIENDLY DEBUG STARTUP SCRIPT
# This script starts debugging with minimal setup required

echo "🤖 BEGINNER-FRIENDLY DEBUG SETUP"
echo "================================"
echo ""
echo "Starting your dashboard with automatic issue detection..."
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from your project root directory"
    echo "   (The folder that contains package.json)"
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies first..."
    npm install
fi

echo "🔧 Adding Debug Panel to Dashboard..."

# Add Debug Panel to Dashboard if not already present
DASHBOARD_FILE="src/pages/Dashboard.tsx"
if [ -f "$DASHBOARD_FILE" ]; then
    if ! grep -q "DebugPanel" "$DASHBOARD_FILE"; then
        echo "   Adding import..."
        # Add import after the last import line
        sed -i.bak '/^import.*from/a\
import DebugPanel from '\''@/components/debug/DebugPanel'\'';
' "$DASHBOARD_FILE"
        
        echo "   Adding component..."
        # Add component before the last closing div
        sed -i.bak 's|</div>$|      <DebugPanel />\
    </div>|' "$DASHBOARD_FILE"
        
        echo "✅ Debug Panel added successfully!"
    else
        echo "✅ Debug Panel already exists!"
    fi
else
    echo "⚠️  Could not find Dashboard.tsx - you may need to add the Debug Panel manually"
fi

echo ""
echo "🚀 Starting development server with debugging enabled..."
echo ""
echo "👀 WHAT TO LOOK FOR:"
echo "- A 'Smart Debug Assistant' panel in the bottom-right corner"
echo "- Red alerts for critical issues"
echo "- Auto-fix buttons for common problems"
echo ""
echo "🔧 IF YOU SEE ISSUES:"
echo "1. Read the description (explains what's wrong)"
echo "2. Click 'Auto-Fix This Issue' if available"
echo "3. If that doesn't work, refresh the page (F5)"
echo ""
echo "📖 For detailed help, see: BEGINNER_SETUP.md"
echo ""
echo "Starting in 3 seconds..."
sleep 3

# Start the development server with debug flags
DEBUG=dashboard:*,supabase:*,musickit:* npm run dev &

# Get the process ID
DEV_PID=$!

# Wait a moment for the server to start
sleep 5

# Try to open the browser
URL="http://localhost:3000/dashboard"
echo ""
echo "🌐 Opening browser to: $URL"

if command -v open >/dev/null 2>&1; then
    # macOS
    open "$URL"
elif command -v xdg-open >/dev/null 2>&1; then
    # Linux
    xdg-open "$URL"
elif command -v start >/dev/null 2>&1; then
    # Windows (Git Bash)
    start "$URL"
else
    echo "   Please manually open your browser and go to: $URL"
fi

echo ""
echo "🤖 The Smart Debug Assistant will guide you from here!"
echo ""
echo "Press Ctrl+C to stop the debug server when you're done."

# Wait for the development server process
wait $DEV_PID
