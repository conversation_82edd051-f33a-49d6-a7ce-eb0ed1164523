# Dashboard Loading Debug Guide

## 🚀 Quick Start

### 1. Launch Debug Session
```bash
# Option A: Use VS Code Debug Configuration
# Press F5 and select "🎯 Full Dashboard Debug Session"

# Option B: Manual launch with debug environment
DEBUG=dashboard:*,supabase:*,musickit:* npm run dev
```

### 2. Open Chrome DevTools
- Press F12 in browser
- Go to Console tab
- Enable "Preserve log" to keep logs during navigation
- Set Console filter to show all levels (Verbose, Info, Warnings, Errors)

## 🔧 VS Code Debug Configuration

The `.vscode/launch.json` includes these configurations:

- **🎯 Full Dashboard Debug Session**: Launches both server and browser with debugging
- **🔍 Dashboard Debug - Full Stack**: Server-side debugging with environment variables
- **🌐 Chrome - Dashboard Debug**: Browser debugging with breakpoints
- **🔧 Attach to Running Dev Server**: Attach to existing dev server
- **🚨 Exception Catcher**: Catches uncaught exceptions and promise rejections

## 📊 Debug Namespaces

The debug utility uses these namespaces:

- `dashboard:data` - Data fetching and state management
- `dashboard:realtime` - Supabase real-time subscriptions
- `dashboard:musickit` - MusicKit integration
- `dashboard:auth` - Authentication flow
- `supabase:query` - Database queries
- `supabase:realtime` - Real-time events
- `musickit:lifecycle` - MusicKit lifecycle events
- `musickit:auth` - MusicKit authentication

## 🎯 Key Breakpoint Locations

### useDashboardData.ts
```typescript
// Line ~110: fetchAllData function start
// Line ~125: Before parallel queries
// Line ~145: After query results
// Line ~170: State updates
// Line ~200: Realtime subscription setup
```

### useAppleMusicAuth.ts
```typescript
// Line ~75: MusicKit initialization start
// Line ~100: Initialization success/failure
// Line ~180: syncConnectionState start
// Line ~200: Authorization check
// Line ~240: Token validation
```

### AuthContext.tsx
```typescript
// Line ~40: Initial session check
// Line ~60: Auth state change handler
// Line ~90: Onboarding status check
```

## 🔍 What to Look For

### 1. Loading State Issues
**Symptoms**: Spinners never stop, components stuck in loading state

**Debug Steps**:
1. Check `dashboard:data` logs for `fetchAllData` completion
2. Verify `loading` state transitions in console
3. Look for uncaught errors in Promise chains
4. Check if `setLoading(false)` is called in finally blocks

**Key Logs to Monitor**:
```
✅ DASHBOARD_DATA: fetchAllData called
✅ DASHBOARD_DATA: Parallel query results
✅ DASHBOARD_DATA: State change - loading: true -> false
```

### 2. MusicKit Initialization Problems
**Symptoms**: Apple Music never connects, player doesn't initialize

**Debug Steps**:
1. Monitor `musickit:lifecycle` for initialization attempts
2. Check browser console for MusicKit errors
3. Verify Apple Music developer token validity
4. Look for network connectivity issues

**Key Logs to Monitor**:
```
✅ MUSICKIT: Starting MusicKit initialization
✅ MUSICKIT: Initialization successful
✅ MUSICKIT: Authorization check complete
```

### 3. Real-time Subscription Issues
**Symptoms**: Data doesn't update when changed elsewhere

**Debug Steps**:
1. Check `dashboard:realtime` for subscription setup
2. Monitor Supabase connection status
3. Verify channel subscriptions are active
4. Look for subscription cleanup issues

**Key Logs to Monitor**:
```
✅ DASHBOARD_REALTIME: Realtime subscriptions created
✅ DASHBOARD_REALTIME: Session change detected
✅ DASHBOARD_REALTIME: Subscription change detected
```

## 🌐 Network Request Inspection

### Chrome DevTools Network Tab
1. Open DevTools → Network tab
2. Filter by "Fetch/XHR" to see API calls
3. Look for failed requests (red status codes)
4. Check request/response headers and payloads

### Key Requests to Monitor
- `POST /rest/v1/rpc/sessions` - Session queries
- `POST /rest/v1/rpc/dj_subscriptions` - Subscription queries  
- `WebSocket` connections to Supabase realtime
- Apple Music API calls (if any)

### Common Network Issues
- **401 Unauthorized**: Authentication token expired
- **403 Forbidden**: RLS policy blocking access
- **500 Internal Server Error**: Database/server error
- **WebSocket connection failed**: Real-time subscription issues

## 🎵 MusicKit Lifecycle Debugging

### Browser Console Commands
```javascript
// Check MusicKit status
window.MusicKit?.getInstance()?.isAuthorized

// Check developer token
window.MusicKit?.getInstance()?.developerToken

// Monitor MusicKit events
window.MusicKit?.getInstance()?.addEventListener('authorizationStatusDidChange', console.log)
window.MusicKit?.getInstance()?.addEventListener('playbackStateDidChange', console.log)
```

### MusicKit Event Sequence
1. `configure` - MusicKit configuration
2. `authorizationStatusDidChange` - Auth status changes
3. `playbackReady` - Player ready for use
4. `mediaItemDidChange` - Track changes

## 🚨 Common Issues & Solutions

### Issue: Infinite Loading Spinners
**Cause**: `setLoading(false)` not called due to uncaught error
**Solution**: Check console for errors, ensure finally blocks execute

### Issue: MusicKit Never Initializes  
**Cause**: Network issues, invalid developer token, or browser compatibility
**Solution**: Check network tab, verify token, test in different browser

### Issue: Real-time Updates Not Working
**Cause**: WebSocket connection failed or RLS policies blocking
**Solution**: Check WebSocket in network tab, verify database permissions

### Issue: Excessive Re-renders
**Cause**: Dependency arrays causing infinite loops
**Solution**: Monitor render counts in debug logs, check useEffect dependencies

## 📝 Debug Log Analysis

### Successful Load Sequence
```
[timestamp] dashboard:auth - AuthContext: Initializing AuthContext
[timestamp] dashboard:auth - Initial session check
[timestamp] dashboard:data - useDashboardData: Hook initialized  
[timestamp] dashboard:data - Starting fetchAllData
[timestamp] supabase:query - GET parallel queries
[timestamp] dashboard:data - Parallel query results
[timestamp] dashboard:data - State change - sessions
[timestamp] dashboard:realtime - Realtime subscriptions created
[timestamp] musickit:lifecycle - Starting MusicKit initialization
[timestamp] musickit:lifecycle - Initialization successful
```

### Failed Load Indicators
```
❌ Error logs without corresponding success logs
❌ Loading state never transitions to false
❌ Missing "fetchAllData" completion logs
❌ MusicKit initialization errors
❌ WebSocket connection failures
```

## 🔧 Advanced Debugging

### Performance Profiling
1. Chrome DevTools → Performance tab
2. Record while loading dashboard
3. Look for long tasks, excessive renders
4. Check memory usage patterns

### React DevTools
1. Install React DevTools extension
2. Use Profiler to identify slow components
3. Check component re-render patterns
4. Monitor state changes

### Memory Leaks
1. Check for uncleaned subscriptions
2. Monitor memory usage over time
3. Look for retained objects in heap snapshots
4. Verify cleanup functions execute

## 📋 Step-by-Step Debugging Checklist

### Phase 1: Setup (5 minutes)
- [ ] Launch debug session: `npm run dev:debug` or VS Code F5
- [ ] Open Chrome DevTools (F12)
- [ ] Enable "Preserve log" in Console
- [ ] Set Console filter to "All levels"
- [ ] Open Network tab, enable "Preserve log"
- [ ] Navigate to `/dashboard`

### Phase 2: Initial Load Analysis (10 minutes)
- [ ] Check for authentication logs: `dashboard:auth`
- [ ] Verify initial session check completes
- [ ] Monitor `useDashboardData` hook initialization
- [ ] Look for `fetchAllData` start/completion logs
- [ ] Check parallel query results in console
- [ ] Verify loading state transitions: `true → false`

### Phase 3: MusicKit Investigation (10 minutes)
- [ ] Monitor `musickit:lifecycle` initialization logs
- [ ] Check for MusicKit configuration errors
- [ ] Verify Apple Music developer token validity
- [ ] Test MusicKit status: `window.MusicKit?.getInstance()?.isAuthorized`
- [ ] Look for authorization flow completion

### Phase 4: Real-time Subscriptions (5 minutes)
- [ ] Check `dashboard:realtime` subscription setup
- [ ] Verify WebSocket connections in Network tab
- [ ] Test real-time updates by changing data elsewhere
- [ ] Monitor subscription cleanup on unmount

### Phase 5: Network & Performance (10 minutes)
- [ ] Review all API calls in Network tab
- [ ] Check for failed requests (4xx, 5xx status codes)
- [ ] Verify Supabase RLS policies allow access
- [ ] Monitor memory usage and potential leaks
- [ ] Check for excessive re-renders in React DevTools

### Phase 6: Error Analysis (Ongoing)
- [ ] Document all error messages with timestamps
- [ ] Check browser console for uncaught exceptions
- [ ] Review promise rejection logs
- [ ] Identify the exact point where loading stops
- [ ] Correlate errors with specific user actions

## 🎯 Quick Debug Commands

```bash
# Start with comprehensive debugging
npm run dev:debug

# Focus on data fetching only
npm run dev:debug-data

# Focus on MusicKit issues only
npm run dev:debug-musickit

# Enable all debug output (verbose)
npm run dev:debug-verbose
```

## 🔍 Browser Console Quick Checks

```javascript
// Check current loading states
console.log('Auth loading:', window.__authLoading);
console.log('Data loading:', window.__dataLoading);

// Check MusicKit status
console.log('MusicKit authorized:', window.MusicKit?.getInstance()?.isAuthorized);

// Check Supabase connection
console.log('Supabase client:', window.supabaseClient);

// Force refresh dashboard data (if exposed)
window.__forceDashboardRefresh?.();
```

## 🚨 Red Flags to Watch For

1. **Loading never transitions to false**
2. **fetchAllData called but never completes**
3. **MusicKit initialization attempts but fails silently**
4. **WebSocket connections failing in Network tab**
5. **401/403 errors on Supabase queries**
6. **Uncaught promise rejections**
7. **Excessive component re-renders (>10)**
8. **Memory usage continuously increasing**

## 📞 When to Escalate

If after following this guide you still can't identify the issue:

1. **Capture a complete debug session**:
   - Full console output with timestamps
   - Network tab HAR file export
   - React DevTools profiler recording
   - Screenshots of stuck loading states

2. **Document the exact reproduction steps**:
   - Browser version and OS
   - Specific user actions that trigger the issue
   - Whether it happens on fresh page load vs. navigation
   - Any error messages or unusual behavior

3. **Test in different environments**:
   - Different browsers (Chrome, Firefox, Safari)
   - Incognito/private mode
   - Different network conditions
   - Different user accounts

Remember: The goal is to trace the exact point where loading states get stuck and identify the root cause through systematic debugging.
