#!/usr/bin/env node

/**
 * 🤖 ONE-CLICK DEBUG SETUP
 *
 * This script automatically sets up debugging for complete beginners.
 * Just run: node debug-setup.js
 *
 * What it does:
 * 1. Adds the Debug Panel to your Dashboard
 * 2. Starts the development server with debugging enabled
 * 3. Opens your browser to the dashboard
 * 4. Shows you exactly what to look for
 */

import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';

// Get current directory in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🤖 BEGINNER-FRIENDLY DEBUG SETUP');
console.log('================================');
console.log('');
console.log('This will automatically set up debugging for your dashboard.');
console.log('No technical knowledge required!');
console.log('');

// Step 1: Add Debug Panel to Dashboard
console.log('📝 Step 1: Adding Debug Panel to your Dashboard...');

const dashboardPath = path.join(__dirname, 'src', 'pages', 'Dashboard.tsx');

if (fs.existsSync(dashboardPath)) {
  let dashboardContent = fs.readFileSync(dashboardPath, 'utf8');
  
  // Check if Debug Panel is already added
  if (!dashboardContent.includes('DebugPanel')) {
    // Add import
    if (!dashboardContent.includes("import DebugPanel from '@/components/debug/DebugPanel';")) {
      const importIndex = dashboardContent.indexOf("import { DJProfile } from '@/lib/types';");
      if (importIndex !== -1) {
        const insertPoint = dashboardContent.indexOf('\n', importIndex) + 1;
        dashboardContent = dashboardContent.slice(0, insertPoint) + 
          "import DebugPanel from '@/components/debug/DebugPanel';\n" + 
          dashboardContent.slice(insertPoint);
      }
    }
    
    // Add component before closing div
    const lastDivIndex = dashboardContent.lastIndexOf('</div>');
    if (lastDivIndex !== -1) {
      dashboardContent = dashboardContent.slice(0, lastDivIndex) + 
        '      <DebugPanel />\n    ' + 
        dashboardContent.slice(lastDivIndex);
    }
    
    fs.writeFileSync(dashboardPath, dashboardContent);
    console.log('✅ Debug Panel added to Dashboard!');
  } else {
    console.log('✅ Debug Panel already exists in Dashboard!');
  }
} else {
  console.log('❌ Could not find Dashboard.tsx file');
}

// Step 2: Create beginner instructions
console.log('');
console.log('📋 Step 2: Creating beginner instructions...');

const instructionsContent = `
# 🤖 BEGINNER'S DEBUG GUIDE

## What You'll See

When you open your dashboard, you'll see a **Smart Debug Assistant** panel in the bottom-right corner.

### 🚦 Status Colors:
- **🔴 RED**: Critical problem - needs immediate attention
- **🟡 YELLOW**: Warning - might slow things down but not broken
- **🔵 BLUE**: Loading - everything is working, just wait
- **🟢 GREEN**: All good - everything is working perfectly!

## What To Do When You See Problems

### 🚨 If You See Red (Critical Issues):
1. **Read the description** - it explains what's wrong in plain English
2. **Click "Auto-Fix This Issue"** - this will try to fix it automatically
3. **If auto-fix doesn't work**, try refreshing the page (press F5)
4. **Still broken?** Click "Get Help & Copy Debug Info" and share the information

### ⚠️ If You See Yellow (Warnings):
- These usually don't break anything
- You can often ignore them
- If the app feels slow, try the auto-fix button

### 🔵 If You See Blue (Loading):
- This is normal! Just wait for it to finish
- If it stays blue for more than 30 seconds, there might be a problem

## Common Issues & Solutions

### "Dashboard Stuck Loading"
- **What it means**: Your dashboard started loading but got stuck
- **Solution**: Click "Auto-Fix This Issue" or refresh the page

### "Login Problem Detected"  
- **What it means**: There's an issue with your login
- **Solution**: Refresh the page or log out and log back in

### "Apple Music Connection Failed"
- **What it means**: The music player couldn't connect
- **Solution**: Click "Auto-Fix This Issue" or check your internet

### "Performance Issue Detected"
- **What it means**: The app is running slower than normal
- **Solution**: Usually fixes itself, but you can refresh if it's very slow

## Emergency Actions

If everything is broken:
1. **Refresh the page** (press F5)
2. **Clear your browser cache** (Ctrl+Shift+Delete)
3. **Try a different browser** (Chrome, Firefox, Safari)
4. **Check your internet connection**

## Getting Help

If you're still stuck:
1. Click "Get Help & Copy Debug Info" in the debug panel
2. Take a screenshot of any error messages
3. Note what you were doing when the problem started
4. Share this information with a developer

Remember: The debug panel is your friend! It will tell you exactly what's wrong and how to fix it.
`;

fs.writeFileSync('BEGINNER_DEBUG_GUIDE.md', instructionsContent);
console.log('✅ Beginner instructions created!');

// Step 3: Start debug server
console.log('');
console.log('🚀 Step 3: Starting debug server...');
console.log('');
console.log('The server will start with automatic issue detection enabled.');
console.log('Your browser will open automatically to the dashboard.');
console.log('');
console.log('👀 WHAT TO LOOK FOR:');
console.log('- A "Smart Debug Assistant" panel in the bottom-right corner');
console.log('- Red alerts for critical issues');
console.log('- Auto-fix buttons for common problems');
console.log('');
console.log('🔧 IF YOU SEE ISSUES:');
console.log('1. Read the description (explains what\'s wrong)');
console.log('2. Click "Auto-Fix This Issue" if available');
console.log('3. If that doesn\'t work, refresh the page (F5)');
console.log('');
console.log('Starting server in 3 seconds...');

setTimeout(() => {
  // Start the development server with debug flags
  const debugProcess = spawn('npm', ['run', 'dev:debug'], {
    stdio: 'inherit',
    shell: true
  });

  // Open browser after a delay
  setTimeout(() => {
    const open = require('child_process').spawn;
    const url = 'http://localhost:3000/dashboard';
    
    // Try to open browser (works on most systems)
    try {
      if (process.platform === 'darwin') {
        spawn('open', [url]);
      } else if (process.platform === 'win32') {
        spawn('start', [url], { shell: true });
      } else {
        spawn('xdg-open', [url]);
      }
      console.log('');
      console.log('🌐 Browser should open automatically to: ' + url);
      console.log('');
      console.log('If it doesn\'t open, manually go to: http://localhost:3000/dashboard');
    } catch (error) {
      console.log('');
      console.log('🌐 Please manually open your browser and go to:');
      console.log('   http://localhost:3000/dashboard');
    }
    
    console.log('');
    console.log('📖 For detailed help, see: BEGINNER_DEBUG_GUIDE.md');
    console.log('');
    console.log('🤖 The Smart Debug Assistant will guide you from here!');
  }, 5000);

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping debug server...');
    debugProcess.kill();
    process.exit();
  });

}, 3000);
