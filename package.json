{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "debug": "node debug-setup.js", "debug-beginner": "node debug-setup.js", "dev:debug": "DEBUG=dashboard:*,supabase:*,musickit:* vite", "dev:debug-verbose": "DEBUG=* vite", "dev:debug-data": "DEBUG=dashboard:data,supabase:query vite", "dev:debug-musickit": "DEBUG=dashboard:musickit,musickit:* vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/storage-js": "^2.5.5", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.71.1", "@types/react-helmet": "^6.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^11.18.2", "gray-matter": "^4.0.3", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "qrcode.react": "^3.1.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "remark-gfm": "^4.0.1", "rss": "^1.2.2", "sharp": "^0.33.5", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.3", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.13.17", "@types/puppeteer": "^5.4.7", "@types/qrcode.react": "^1.0.4", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@types/recharts": "^1.8.29", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.4.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^8.56.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.47", "puppeteer": "^24.9.0", "tailwindcss": "^3.4.11", "typescript": "^5.3.3", "vite": "^6.3.5", "vite-plugin-remove-console": "^2.2.0"}}